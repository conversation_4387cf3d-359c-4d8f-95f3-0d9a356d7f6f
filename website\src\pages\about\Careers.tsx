import React from 'react';
import {
  Wrench,
  Clock,
  Mail,
  Phone
} from 'lucide-react';
import PageLayout from '../../components/layout/PageLayout';

const Careers: React.FC = () => {
  return (
    <PageLayout hideHero hideBreadcrumbs>
      <div className="font-['Open_Sans']">
        {/* Apply Open Sans font family consistently */}
        <style>{`
          nav.mb-10 { display: none !important; }
          .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }

          /* Apply Open Sans font family consistently */
          * {
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
          }

          /* Ensure proper font weights and sizes */
          h1, h2, h3, h4, h5, h6 {
            font-family: 'Open Sans', sans-serif !important;
            font-weight: 700 !important;a
          }

          p, span, div {
            font-family: 'Open Sans', sans-serif !important;
          }

          button {
            font-family: 'Open Sans', sans-serif !important;
            font-weight: 600 !important;
          }
        `}</style>

        {/* Under Maintenance Section */}
        <section className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">

            {/* Maintenance Icon */}
            <div className="mb-8">
              <div className="bg-blue-100 rounded-full p-6 w-24 h-24 mx-auto flex items-center justify-center">
                <Wrench className="w-12 h-12 text-blue-600" />
              </div>
            </div>

            {/* Main Heading */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Careers Page Under Maintenance
            </h1>

            {/* Description */}
            <p className="text-lg md:text-xl text-gray-600 mb-8 leading-relaxed">
              We're currently updating our careers page to bring you a better experience.
              Please check back soon for exciting job opportunities at Atandra Energy.
            </p>

            {/* Status Info */}
            <div className="bg-white rounded-lg p-6 shadow-lg border border-gray-100 mb-8">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <Clock className="w-5 h-5 text-blue-600" />
                <span className="text-gray-700 font-medium">Expected completion: Soon</span>
              </div>
              <p className="text-gray-600 text-sm">
                We're working hard to complete the updates and will be back online shortly.
              </p>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              <p className="text-gray-700 font-medium">
                In the meantime, feel free to reach out to us directly:
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-300 flex items-center justify-center space-x-2"
                >
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </a>

                <a
                  href="tel:+919500097966"
                  className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-600 hover:text-white transition-colors duration-300 flex items-center justify-center space-x-2"
                >
                  <Phone className="w-4 h-4" />
                  <span>+91 95000 97966</span>
                </a>
              </div>
            </div>

            {/* Back to Home */}
            <div className="mt-8">
              <a
                href="/"
                className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300"
              >
                ← Back to Home
              </a>
            </div>
          </div>
        </section>
      </div>
    </PageLayout>
  );
};

export default Careers;