const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransporter({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
        tls: {
          rejectUnauthorized: false, // Allow self-signed certificates
          ciphers: 'SSLv3' // For Yahoo compatibility
        },
        debug: process.env.NODE_ENV === 'development', // Enable debug logs
        logger: process.env.NODE_ENV === 'development' // Enable logger
      });

      console.log('✅ Email transporter initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize email transporter:', error);
      throw error;
    }
  }

  async verifyConnection() {
    try {
      await this.transporter.verify();
      console.log('✅ SMTP connection verified');
      return true;
    } catch (error) {
      console.error('❌ SMTP connection failed:', error);
      return false;
    }
  }

  generateEmailHTML(formData) {
    const {
      name,
      email,
      company,
      designation,
      city,
      mobile,
      pincode,
      products,
      remarks,
      requestDemo,
      requestCallback,
      sendDetails,
      sendUpdates
    } = formData;

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Sales Enquiry</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #000000, #333333); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #555; }
            .value { margin-left: 10px; }
            .checkbox-section { background: #e8f4f8; padding: 15px; border-radius: 5px; margin-top: 20px; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔔 New Sales Enquiry</h1>
                <p>Atandra Energy Private Limited</p>
            </div>
            <div class="content">
                <h2>Contact Information</h2>
                <div class="field"><span class="label">Name:</span><span class="value">${name}</span></div>
                <div class="field"><span class="label">Email:</span><span class="value">${email}</span></div>
                <div class="field"><span class="label">Company:</span><span class="value">${company}</span></div>
                <div class="field"><span class="label">Designation:</span><span class="value">${designation}</span></div>
                <div class="field"><span class="label">City:</span><span class="value">${city}</span></div>
                <div class="field"><span class="label">Mobile:</span><span class="value">${mobile}</span></div>
                <div class="field"><span class="label">Pincode:</span><span class="value">${pincode}</span></div>
                
                <h2>Product Interest</h2>
                <div class="field"><span class="label">Products Interested:</span><span class="value">${products}</span></div>
                
                ${remarks ? `<h2>Additional Information</h2><div class="field"><span class="label">Remarks:</span><span class="value">${remarks}</span></div>` : ''}
                
                <div class="checkbox-section">
                    <h3>Service Requests</h3>
                    ${requestDemo ? '<div>✅ Request Demo</div>' : ''}
                    ${requestCallback ? '<div>✅ Request Call Back</div>' : ''}
                    ${sendDetails ? '<div>✅ Send Product Details</div>' : ''}
                    ${sendUpdates ? '<div>✅ Send Product Updates</div>' : ''}
                </div>
                
                <div class="footer">
                    <p>This enquiry was submitted on ${new Date().toLocaleString()}</p>
                    <p>Please respond promptly to maintain customer satisfaction.</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  async sendSalesEnquiry(formData) {
    try {
      // Create mail options with proper Yahoo configuration
      const mailOptions = {
        from: {
          name: process.env.FROM_NAME || 'Atandra Energy Sales Team',
          address: process.env.FROM_EMAIL || process.env.SMTP_USER // Use SMTP_USER if FROM_EMAIL not set
        },
        to: process.env.TO_EMAIL || process.env.SMTP_USER, // Use SMTP_USER if TO_EMAIL not set
        subject: `🔔 New Sales Enquiry from ${formData.name} - ${formData.company}`,
        html: this.generateEmailHTML(formData),
        replyTo: formData.email,
        // Additional headers for better deliverability
        headers: {
          'X-Priority': '1',
          'X-MSMail-Priority': 'High',
          'Importance': 'high'
        }
      };

      console.log('📧 Attempting to send email with options:', {
        from: mailOptions.from,
        to: mailOptions.to,
        subject: mailOptions.subject,
        replyTo: mailOptions.replyTo
      });

      const result = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId,
        message: 'Email sent successfully'
      };
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      
      // Log more detailed error information
      if (error.code) {
        console.error('Error code:', error.code);
      }
      if (error.response) {
        console.error('SMTP Response:', error.response);
      }
      if (error.responseCode) {
        console.error('Response Code:', error.responseCode);
      }
      
      throw error;
    }
  }

  async sendConfirmationEmail(formData) {
    try {
      const confirmationHTML = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Thank You for Your Enquiry</title>
          <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background: linear-gradient(135deg, #000000, #333333); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
              .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
              .footer { text-align: center; margin-top: 20px; color: #666; }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="header">
                  <h1>Thank You!</h1>
                  <p>Atandra Energy Private Limited</p>
              </div>
              <div class="content">
                  <h2>Dear ${formData.name},</h2>
                  <p>Thank you for your enquiry about our energy solutions. We have received your request and our sales team will get back to you within 24 hours.</p>
                  
                  <h3>Your Enquiry Details:</h3>
                  <p><strong>Company:</strong> ${formData.company}</p>
                  <p><strong>Products of Interest:</strong> ${formData.products}</p>
                  
                  <p>In the meantime, feel free to explore our comprehensive product catalogue and learn more about our innovative energy management solutions.</p>
                  
                  <div class="footer">
                      <p><strong>Contact Information:</strong></p>
                      <p>📧 <EMAIL></p>
                      <p>📞 +91 95000 97966</p>
                      <p>📍 No.5, Kumaran St, Pazhvanthangal, Chennai, Tamil Nadu, India, 600114</p>
                  </div>
              </div>
          </div>
      </body>
      </html>
      `;

      const mailOptions = {
        from: {
          name: process.env.FROM_NAME || 'Atandra Energy Sales Team',
          address: process.env.FROM_EMAIL || process.env.SMTP_USER
        },
        to: formData.email,
        subject: 'Thank you for your enquiry - Atandra Energy',
        html: confirmationHTML,
        headers: {
          'X-Priority': '3',
          'X-MSMail-Priority': 'Normal'
        }
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('✅ Confirmation email sent successfully:', result.messageId);
      
      return {
        success: true,
        messageId: result.messageId,
        message: 'Confirmation email sent successfully'
      };
    } catch (error) {
      console.error('❌ Failed to send confirmation email:', error);
      throw error;
    }
  }
}

module.exports = new EmailService();