<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Request Assignment - {{ service_center }} - {{ company_name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 750px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 25px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 26px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .assignment-banner {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 15px 20px;
            margin: 0;
            border-top: 1px solid #d1fae5;
        }
        
        .assignment-banner h3 {
            color: #059669;
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .assignment-banner p {
            color: #047857;
            font-size: 14px;
        }
        
        .content {
            padding: 30px 25px;
        }
        
        .priority-section {
            background-color: #fff7ed;
            border: 2px solid #fb923c;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .priority-section h3 {
            color: #ea580c;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        
        .priority-high {
            background-color: #fef2f2;
            border-color: #dc2626;
        }
        
        .priority-high h3 {
            color: #dc2626;
        }
        
        .priority-medium {
            background-color: #fffbeb;
            border-color: #f59e0b;
        }
        
        .priority-medium h3 {
            color: #d97706;
        }
        
        .priority-low {
            background-color: #f0fdf4;
            border-color: #10b981;
        }
        
        .priority-low h3 {
            color: #059669;
        }
        
        .ticket-info {
            background-color: #eff6ff;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .ticket-info h3 {
            color: #1d4ed8;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .ticket-id {
            background-color: #1e40af;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 16px;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .service-center-info {
            background-color: #f0fdf4;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .service-center-info h3 {
            color: #059669;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #bbf7d0;
            padding-bottom: 10px;
        }
        
        .product-info {
            background-color: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .product-info h3 {
            color: #d97706;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #fbbf24;
            padding-bottom: 10px;
        }
        
        .customer-info {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .customer-info h3 {
            color: #1e40af;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            padding: 15px;
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #1f2937;
            font-size: 16px;
        }
        
        .problem-section {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .problem-section h3 {
            color: #dc2626;
            margin-bottom: 20px;
            font-size: 20px;
            border-bottom: 2px solid #fecaca;
            padding-bottom: 10px;
        }
        
        .problem-description {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin-top: 15px;
            font-style: italic;
            color: #374151;
            line-height: 1.7;
        }
        
        .action-items {
            background-color: #ecfdf5;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .action-items h3 {
            color: #059669;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .action-list {
            list-style: none;
            padding: 0;
        }
        
        .action-list li {
            background-color: #ffffff;
            border: 1px solid #d1fae5;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .action-list li::before {
            content: "✓";
            background-color: #10b981;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }
        
        .quick-action-btn {
            display: inline-block;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-secondary {
            background-color: #10b981;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #059669;
        }
        
        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }
        
        .btn-warning:hover {
            background-color: #d97706;
        }
        
        .priority-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .priority-high {
            background-color: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .priority-medium {
            background-color: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        
        .priority-low {
            background-color: #f0fdf4;
            color: #059669;
            border: 1px solid #bbf7d0;
        }
        
        .footer {
            background-color: #f8fafc;
            padding: 25px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
        }
        
        .footer h4 {
            color: #374151;
            margin-bottom: 15px;
        }
        
        .timestamp {
            background-color: #f1f5f9;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
            font-size: 14px;
        }
        
        .timestamp p {
            margin-bottom: 5px;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔧 Service Request Assignment</h1>
            <p>{{ service_center }} Service Center - {{ company_name }}</p>
        </div>

        <!-- Assignment Banner -->
        <div class="assignment-banner">
            <h3>📋 New Service Request Assigned</h3>
            <p>A technical support request has been assigned to your service center. Priority level: <strong>{{ priority_level | upper }}</strong></p>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Priority Section -->
            <div class="priority-section priority-{{ priority_level }}">
                <h3>
                    {% if priority_level == 'high' %}
                        🔴 HIGH PRIORITY REQUEST
                    {% elif priority_level == 'medium' %}
                        🟡 MEDIUM PRIORITY REQUEST
                    {% else %}
                        🟢 LOW PRIORITY REQUEST
                    {% endif %}
                </h3>
                <p><strong>Expected Response Time:</strong>
                    {% if priority_level == 'high' %}
                        Within 2-4 hours
                    {% elif priority_level == 'medium' %}
                        Within 4-8 hours
                    {% else %}
                        Within 24 hours
                    {% endif %}
                </p>
                <p><strong>Service Type:</strong> {{ call_type }} - {{ call_category }}</p>
                <p><strong>Current Status:</strong> {{ call_condition }}</p>
            </div>

            <!-- Ticket Information -->
            <div class="ticket-info">
                <h3>🎫 Service Ticket Information</h3>
                <div class="ticket-id">Ticket ID: {{ ticket_id }}</div>
                <p><strong>Created:</strong> {{ submission_date }}</p>
                <p><strong>Priority Level:</strong>
                    <span class="priority-badge priority-{{ priority_level }}">{{ priority_level }} Priority</span>
                </p>
                <p><strong>Assigned Service Center:</strong> {{ service_center }}</p>
                {% if has_preferred_time %}
                <p><strong>Customer Preferred Time:</strong> {{ preferred_time }}</p>
                {% endif %}
            </div>

            <!-- Service Center Assignment -->
            <div class="service-center-info">
                <h3>🏢 Service Center Assignment</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Assigned Center</span>
                        <span class="info-value">{{ service_center }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Request Type</span>
                        <span class="info-value">{{ call_type }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Category</span>
                        <span class="info-value">{{ call_category }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Current Condition</span>
                        <span class="info-value">{{ call_condition }}</span>
                    </div>
                </div>
            </div>

            <!-- Product Information -->
            <div class="product-info">
                <h3>📦 Product Details</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Brand</span>
                        <span class="info-value">{{ brand }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Model</span>
                        <span class="info-value">{{ model }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Serial Number</span>
                        <span class="info-value">{{ serial_number }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Category</span>
                        <span class="info-value">{{ product_category }}</span>
                    </div>
                    {% if has_rating %}
                    <div class="info-item">
                        <span class="info-label">Customer Rating</span>
                        <span class="info-value">{{ rating }}/5 ⭐</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Customer Information -->
            <div class="customer-info">
                <h3>👤 Customer Information</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Company</span>
                        <span class="info-value">{{ company }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Contact Person</span>
                        <span class="info-value">{{ contact_person }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Mobile Number</span>
                        <span class="info-value">{{ mobile_number }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email</span>
                        <span class="info-value">{{ email }}</span>
                    </div>
                    {% if has_alternative_contact %}
                    <div class="info-item">
                        <span class="info-label">Alternative Contact</span>
                        <span class="info-value">{{ alternative_contact }}</span>
                    </div>
                    {% endif %}
                </div>

                <div style="margin-top: 20px;">
                    <div class="info-item">
                        <span class="info-label">Company Address</span>
                        <span class="info-value">{{ company_address }}</span>
                    </div>
                </div>
            </div>

            <!-- Problem Description -->
            <div class="problem-section">
                <h3>🔍 Problem Description</h3>
                <p><strong>Issue Category:</strong> {{ call_category }}</p>
                <p><strong>Current Condition:</strong> {{ call_condition }}</p>

                <div class="problem-description">
                    {{ problem_description }}
                </div>
            </div>

            <!-- Action Items -->
            <div class="action-items">
                <h3>📋 Required Actions</h3>
                <ul class="action-list">
                    <li>Contact customer within the priority response time frame</li>
                    <li>Schedule service appointment based on customer preference</li>
                    <li>Prepare necessary tools and parts for {{ brand }} {{ model }}</li>
                    <li>Review product manual and service guidelines</li>
                    <li>Update ticket status in the service management system</li>
                    <li>Provide regular updates to customer and support team</li>
                </ul>
            </div>

            <!-- Quick Actions -->
            <div style="text-align: center; margin: 30px 0;">
                <h3 style="color: #059669; margin-bottom: 20px;">🚀 Quick Actions</h3>
                <div class="quick-actions">
                    <a href="mailto:{{ email }}?subject=Service Request {{ ticket_id }} - {{ brand }} {{ model }}&body=Dear {{ contact_person }},%0D%0A%0D%0AThank you for contacting {{ service_center }} Service Center regarding your {{ brand }} {{ model }} (Serial: {{ serial_number }}).%0D%0A%0D%0ATicket ID: {{ ticket_id }}%0D%0AStatus: Assigned to Service Center%0D%0A%0D%0AWe have received your service request and will contact you shortly to schedule an appointment..." class="quick-action-btn btn-primary">
                        📧 Contact Customer
                    </a>

                    <a href="tel:{{ mobile_number }}" class="quick-action-btn btn-secondary">
                        📞 Call Customer
                    </a>

                    <a href="#" class="quick-action-btn btn-warning">
                        📝 Update Ticket Status
                    </a>
                </div>
            </div>

            <!-- Service Guidelines -->
            <div style="background-color: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px; padding: 25px; margin: 25px 0;">
                <h3 style="color: #0369a1; margin-bottom: 20px;">📖 Service Guidelines</h3>
                <ul style="color: #374151; line-height: 1.8;">
                    <li style="margin-bottom: 8px;"><strong>Priority Level:</strong> {{ priority_level | upper }} - Follow {{ priority_level }} priority response protocols</li>
                    <li style="margin-bottom: 8px;"><strong>Customer Company:</strong> {{ company }} - Check for existing service contracts</li>
                    {% if has_preferred_time %}
                    <li style="margin-bottom: 8px;"><strong>Preferred Time:</strong> {{ preferred_time }} - Schedule accordingly</li>
                    {% endif %}
                    <li style="margin-bottom: 8px;"><strong>Request Type:</strong> {{ call_type }} - {{ call_category }}</li>
                    <li style="margin-bottom: 8px;"><strong>Equipment:</strong> {{ brand }} {{ model }} - Ensure technician expertise</li>
                    <li style="margin-bottom: 8px;"><strong>Documentation:</strong> Update service records and customer feedback</li>
                </ul>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <h4>{{ company_name }} Service Network</h4>
            <p>Excellence in Technical Support & Service</p>
            <p style="margin-top: 15px;">
                For service center support: <a href="mailto:{{ technical_support_email }}">{{ technical_support_email }}</a>
            </p>
        </div>

        <!-- Timestamp Footer -->
        <div class="timestamp">
            <p><strong>Service Request Created:</strong> {{ submission_date }}</p>
            <p><strong>Assigned to:</strong> {{ service_center }} Service Center</p>
            <p><strong>Priority Level:</strong>
                <span class="priority-badge priority-{{ priority_level }}">{{ priority_level | upper }} PRIORITY</span>
            </p>
            <p><strong>System:</strong> {{ company_name }} Service Management System</p>
        </div>
    </div>
</body>
</html>
